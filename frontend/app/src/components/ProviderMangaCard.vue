<template>
  <div class="provider-manga-card">
    <div class="relative group cursor-pointer" @click="viewDetails">
      <div
        class="aspect-w-2 aspect-h-3 rounded-lg overflow-hidden bg-gray-200 dark:bg-dark-700"
      >
        <img
          v-if="manga.cover_image || manga.cover_url"
          :src="manga.cover_image || manga.cover_url"
          :alt="manga.title"
          class="w-full h-full object-center object-cover"
          :class="{ 'blur-md': isNsfw && blurNsfw }"
          @error="onImageError"
        />
        <div
          v-else
          class="w-full h-full flex items-center justify-center text-gray-400 dark:text-gray-500"
        >
          <svg
            class="h-12 w-12"
            xmlns="http://www.w3.org/2000/svg"
            fill="none"
            viewBox="0 0 24 24"
            stroke="currentColor"
          >
            <path
              stroke-linecap="round"
              stroke-linejoin="round"
              stroke-width="2"
              d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"
            />
          </svg>
        </div>
      </div>

      <!-- Overlay on hover -->
      <div class="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-30 transition-all duration-200 rounded-lg flex items-center justify-center">
        <div class="opacity-0 group-hover:opacity-100 transition-opacity duration-200">
          <button
            @click.stop="$emit('add-to-library')"
            class="bg-white dark:bg-dark-800 text-gray-900 dark:text-white px-4 py-2 rounded-lg shadow-lg hover:bg-gray-50 dark:hover:bg-dark-700 transition-colors duration-200"
            :disabled="adding"
          >
            <svg
              v-if="adding"
              class="animate-spin -ml-1 mr-2 h-4 w-4 text-current inline"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                class="opacity-25"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                stroke-width="4"
              ></circle>
              <path
                class="opacity-75"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              ></path>
            </svg>
            {{ adding ? 'Adding...' : 'Add to Library' }}
          </button>
        </div>
      </div>

      <!-- NSFW Badge -->
      <div
        v-if="isNsfw"
        class="absolute top-2 right-2 bg-red-500 text-white text-xs px-2 py-1 rounded"
      >
        NSFW
      </div>

      <!-- Status Badge -->
      <div
        v-if="manga.status"
        class="absolute top-2 left-2 bg-blue-500 text-white text-xs px-2 py-1 rounded"
      >
        {{ formatStatus(manga.status) }}
      </div>
    </div>

    <div class="mt-2">
      <h3
        class="text-sm font-medium text-gray-900 dark:text-white truncate"
        :title="manga.title"
      >
        {{ manga.title }}
      </h3>
      <p
        v-if="manga.author"
        class="text-xs text-gray-500 dark:text-gray-400 truncate"
      >
        {{ manga.author }}
      </p>
      <p
        v-if="manga.year"
        class="text-xs text-gray-500 dark:text-gray-400"
      >
        {{ manga.year }}
      </p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed } from "vue";
import { useRouter } from "vue-router";
import { useSettingsStore } from "../stores/settings";

const props = defineProps({
  manga: {
    type: Object,
    required: true,
  },
  providerId: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["add-to-library", "view-details"]);

const router = useRouter();
const settingsStore = useSettingsStore();

const adding = ref(false);

const isNsfw = computed(() => {
  return props.manga.is_nsfw || props.manga.is_explicit;
});

const blurNsfw = computed(() => settingsStore.getNsfwBlur);

const viewDetails = (event) => {
  // Prevent navigation if clicking on action buttons
  if (event.target.closest("button") || event.target.closest("a")) {
    return;
  }

  // Emit event to parent to show details modal instead of navigating
  emit("view-details", props.manga);
};

const formatStatus = (status) => {
  if (!status) return "";
  
  // Capitalize first letter and replace underscores with spaces
  return status
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

const onImageError = (event) => {
  // Hide the image if it fails to load
  event.target.style.display = "none";
  console.warn(
    `Failed to load cover image for ${props.manga.title}: ${event.target.src}`,
  );
};
</script>

<style scoped>
.provider-manga-card {
  transition: all 0.2s ease-in-out;
}

.provider-manga-card:hover {
  transform: translateY(-2px);
}

.aspect-w-2 {
  position: relative;
  padding-bottom: 150%; /* 2:3 aspect ratio */
}

.aspect-w-2 > * {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}
</style>
